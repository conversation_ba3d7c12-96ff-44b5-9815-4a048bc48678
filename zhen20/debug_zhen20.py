#!/usr/bin/env python3
"""
调试 zhen20.py 实现，详细分析计算过程
"""

import pandas as pd
import numpy as np
import os
from zhen20 import custom_indicator, LLV, HHV

def load_stock_data(stock_code):
    """加载股票数据"""
    data_path = os.path.join('..', 'data', f'{stock_code}.csv')
    if not os.path.exists(data_path):
        raise FileNotFoundError(f"数据文件不存在: {data_path}")
    
    data = pd.read_csv(data_path)
    data['date'] = pd.to_datetime(data['date'])
    return data

def debug_calculation(data, target_date):
    """调试特定日期的计算过程"""
    print(f"\n调试日期：{target_date}")
    print("=" * 60)
    
    # 找到目标日期的索引
    target_idx = data[data['date'] == target_date].index[0]
    
    # 获取到目标日期为止的数据
    data_subset = data.iloc[:target_idx + 1].copy()
    
    CLOSE = data_subset['close']
    LOW = data_subset['low']
    HIGH = data_subset['high']
    
    N1, N2 = 3, 21
    
    print(f"目标日期索引：{target_idx}")
    print(f"数据长度：{len(data_subset)}")
    print(f"目标日期收盘价：{CLOSE.iloc[-1]}")
    print(f"目标日期最低价：{LOW.iloc[-1]}")
    print(f"目标日期最高价：{HIGH.iloc[-1]}")
    
    # 计算短期指标的各个组成部分
    print(f"\n短期指标计算 (N1={N1}):")
    llv_low_n1 = LLV(LOW, N1)
    hhv_close_n1 = HHV(CLOSE, N1)
    
    print(f"LLV(LOW, {N1}) = {llv_low_n1.iloc[-1]:.4f}")
    print(f"HHV(CLOSE, {N1}) = {hhv_close_n1.iloc[-1]:.4f}")
    print(f"CLOSE - LLV(LOW, {N1}) = {CLOSE.iloc[-1] - llv_low_n1.iloc[-1]:.4f}")
    print(f"HHV(CLOSE, {N1}) - LLV(LOW, {N1}) = {hhv_close_n1.iloc[-1] - llv_low_n1.iloc[-1]:.4f}")
    
    duanqi_val = 100 * (CLOSE.iloc[-1] - llv_low_n1.iloc[-1]) / (hhv_close_n1.iloc[-1] - llv_low_n1.iloc[-1])
    print(f"短期 = 100 * ({CLOSE.iloc[-1]} - {llv_low_n1.iloc[-1]:.4f}) / ({hhv_close_n1.iloc[-1]:.4f} - {llv_low_n1.iloc[-1]:.4f}) = {duanqi_val:.2f}")
    
    # 计算长期指标的各个组成部分
    print(f"\n长期指标计算 (N2={N2}):")
    llv_low_n2 = LLV(LOW, N2)
    hhv_close_n2 = HHV(CLOSE, N2)
    
    print(f"LLV(LOW, {N2}) = {llv_low_n2.iloc[-1]:.10f}")
    print(f"HHV(CLOSE, {N2}) = {hhv_close_n2.iloc[-1]:.10f}")
    print(f"CLOSE - LLV(LOW, {N2}) = {CLOSE.iloc[-1] - llv_low_n2.iloc[-1]:.10f}")
    print(f"HHV(CLOSE, {N2}) - LLV(LOW, {N2}) = {hhv_close_n2.iloc[-1] - llv_low_n2.iloc[-1]:.10f}")

    changqi_val = 100 * (CLOSE.iloc[-1] - llv_low_n2.iloc[-1]) / (hhv_close_n2.iloc[-1] - llv_low_n2.iloc[-1])
    print(f"长期 = 100 * ({CLOSE.iloc[-1]} - {llv_low_n2.iloc[-1]:.10f}) / ({hhv_close_n2.iloc[-1]:.10f} - {llv_low_n2.iloc[-1]:.10f}) = {changqi_val:.4f}")
    
    # 显示最近N2天的数据，用于验证LLV和HHV的计算
    print(f"\n最近{N2}天的价格数据：")
    recent_data = data_subset.tail(N2)[['date', 'close', 'low', 'high']]
    for i, (_, row) in enumerate(recent_data.iterrows()):
        print(f"  {i+1:2d}. {row['date'].strftime('%Y-%m-%d')}: close={row['close']:.2f}, low={row['low']:.2f}, high={row['high']:.2f}")
    
    print(f"\n在这{N2}天中：")
    print(f"  最低的low = {recent_data['low'].min():.10f}")
    print(f"  最高的close = {recent_data['close'].max():.10f}")

def main():
    """主函数"""
    print("zhen20 指标计算调试")
    print("=" * 60)
    
    # 加载数据
    stock_code = "002203"
    data = load_stock_data(stock_code)
    
    # 调试特定日期
    debug_dates = ['2025-07-15', '2025-07-16']
    
    for debug_date in debug_dates:
        debug_calculation(data, debug_date)
    
    # 运行完整计算并显示结果
    print("\n" + "=" * 60)
    print("完整计算结果")
    print("=" * 60)
    
    result = custom_indicator(
        CLOSE=data['close'],
        LOW=data['low'], 
        HIGH=data['high'],
        N1=3,
        N2=21,
        N3=1
    )
    
    # 将结果添加到数据中
    for key, value in result.items():
        data[key] = value
    
    # 显示验证日期的结果
    for test_date in debug_dates:
        date_data = data[data['date'] == test_date]
        if not date_data.empty:
            row = date_data.iloc[0]
            print(f"\n{test_date}: 短期={row['短期']:.2f}, 长期={row['长期']:.2f}")

if __name__ == "__main__":
    main()
