#!/usr/bin/env python3
"""
使用talib版本的MyTT函数测试zhen20指标
"""

import pandas as pd
import numpy as np
import os
from zhen20 import custom_indicator

def load_stock_data(stock_code):
    """加载股票数据"""
    data_path = os.path.join('..', 'data', f'{stock_code}.csv')
    data = pd.read_csv(data_path)
    data['date'] = pd.to_datetime(data['date'])
    return data

def test_with_talib():
    """使用talib版本的MyTT函数测试"""
    print("使用 talib 版本的 MyTT 函数测试 zhen20 指标")
    print("=" * 60)
    
    # 加载数据
    stock_code = "002203"
    data = load_stock_data(stock_code)
    
    print(f"数据加载完成，共 {len(data)} 条记录")
    print(f"数据日期范围：{data['date'].min()} 到 {data['date'].max()}")
    
    # 计算指标
    result = custom_indicator(
        CLOSE=data['close'],
        LOW=data['low'], 
        HIGH=data['high'],
        N1=3,
        N2=21,
        N3=1
    )
    
    # 将结果添加到数据中
    for key, value in result.items():
        data[key] = value
    
    # 验证特定日期的数据
    test_dates = ['2025-07-15', '2025-07-16']
    expected_values = {
        '2025-07-15': {'短期': 55.56, '长期': 91.49, 'A': 80, 'B': 20},
        '2025-07-16': {'短期': 6.67, '长期': 85.11, 'A': 80, 'B': 20}
    }
    
    print("\n验证结果（使用 talib 版本）：")
    print("-" * 60)
    
    for test_date in test_dates:
        print(f"\n日期：{test_date}")
        
        # 查找对应日期的数据
        date_data = data[data['date'] == test_date]
        
        if date_data.empty:
            print(f"  ❌ 未找到日期 {test_date} 的数据")
            continue
        
        row = date_data.iloc[0]
        expected = expected_values[test_date]
        
        print(f"  预期值 vs 计算值：")
        
        # 检查各个指标
        for indicator in ['短期', '长期', 'A', 'B']:
            expected_val = expected[indicator]
            calculated_val = row[indicator]
            
            # 对于浮点数，允许小的误差
            if indicator in ['短期', '长期']:
                tolerance = 0.1  # 允许0.1的误差
                is_correct = abs(calculated_val - expected_val) <= tolerance
            else:
                is_correct = calculated_val == expected_val
            
            status = "✅" if is_correct else "❌"
            diff = abs(calculated_val - expected_val) if indicator in ['短期', '长期'] else 0
            print(f"    {indicator}: {expected_val} vs {calculated_val:.2f} {status} (差异: {diff:.2f})")
    
    # 详细分析2025-07-16的计算过程
    print(f"\n详细分析 2025-07-16 的计算过程（talib版本）：")
    print("-" * 60)
    
    target_date = '2025-07-16'
    target_idx = data[data['date'] == target_date].index[0]
    
    # 获取21天数据
    start_idx = max(0, target_idx - 20)
    period_data = data.iloc[start_idx:target_idx + 1]
    
    close_val = data.loc[target_idx, 'close']
    
    # 使用talib计算
    import talib as ta
    
    # 获取到目标日期为止的所有数据
    data_subset = data.iloc[:target_idx + 1]
    
    # 使用talib计算21天最高收盘价和最低价
    high_21_array = ta.MAX(data_subset['close'].values.astype(float), 21)
    low_21_array = ta.MIN(data_subset['low'].values.astype(float), 21)
    
    high_21 = high_21_array[target_idx]
    low_21 = low_21_array[target_idx]
    
    print(f"收盘价 C = {close_val}")
    print(f"21天最高收盘价 HHV(C,21) = {high_21}")
    print(f"21天最低价 LLV(L,21) = {low_21}")
    
    changqi = 100 * (close_val - low_21) / (high_21 - low_21)
    print(f"长期 = 100 * ({close_val} - {low_21}) / ({high_21} - {low_21}) = {changqi:.2f}")
    
    # 比较pandas rolling和talib的差异
    print(f"\n比较 pandas rolling 和 talib 的差异：")
    pandas_high_21 = period_data['close'].max()
    pandas_low_21 = period_data['low'].min()
    
    print(f"pandas rolling - HHV: {pandas_high_21}, LLV: {pandas_low_21}")
    print(f"talib - HHV: {high_21}, LLV: {low_21}")
    print(f"差异 - HHV: {abs(pandas_high_21 - high_21):.6f}, LLV: {abs(pandas_low_21 - low_21):.6f}")

if __name__ == "__main__":
    test_with_talib()
