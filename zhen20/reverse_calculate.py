#!/usr/bin/env python3
"""
反推计算，找出同花顺可能使用的数据值
"""

def reverse_calculate():
    """反推计算同花顺可能使用的数据"""
    print("反推计算同花顺可能使用的数据")
    print("=" * 50)
    
    # 已知数据
    close = 10.42  # 2025-07-16收盘价
    expected_changqi = 85.11  # 预期的长期值
    
    print(f"已知：")
    print(f"  收盘价 C = {close}")
    print(f"  预期长期值 = {expected_changqi}")
    
    # 我们计算得到的数据
    our_llv = 9.64
    our_hhv = 10.56
    our_changqi = 100 * (close - our_llv) / (our_hhv - our_llv)
    
    print(f"\n我们的计算：")
    print(f"  LLV = {our_llv}")
    print(f"  HHV = {our_hhv}")
    print(f"  长期 = 100 * ({close} - {our_llv}) / ({our_hhv} - {our_llv}) = {our_changqi:.2f}")
    
    # 假设HHV不变，反推LLV
    print(f"\n假设 HHV = {our_hhv} 不变，反推 LLV：")
    # 85.11 = 100 * (10.42 - LLV) / (10.56 - LLV)
    # 0.8511 = (10.42 - LLV) / (10.56 - LLV)
    # 0.8511 * (10.56 - LLV) = 10.42 - LLV
    # 8.9916 - 0.8511*LLV = 10.42 - LLV
    # LLV - 0.8511*LLV = 10.42 - 8.9916
    # 0.1489*LLV = 1.4284
    # LLV = 1.4284 / 0.1489 = 9.5926
    
    llv_calculated = (our_hhv * expected_changqi/100 - close) / (expected_changqi/100 - 1)
    print(f"  反推的 LLV = {llv_calculated:.4f}")
    
    # 验证
    verify_changqi = 100 * (close - llv_calculated) / (our_hhv - llv_calculated)
    print(f"  验证：100 * ({close} - {llv_calculated:.4f}) / ({our_hhv} - {llv_calculated:.4f}) = {verify_changqi:.2f}")
    
    # 假设LLV不变，反推HHV
    print(f"\n假设 LLV = {our_llv} 不变，反推 HHV：")
    # 85.11 = 100 * (10.42 - 9.64) / (HHV - 9.64)
    # 0.8511 = 0.78 / (HHV - 9.64)
    # HHV - 9.64 = 0.78 / 0.8511
    # HHV = 9.64 + 0.78 / 0.8511
    
    hhv_calculated = our_llv + (close - our_llv) / (expected_changqi/100)
    print(f"  反推的 HHV = {hhv_calculated:.4f}")
    
    # 验证
    verify_changqi2 = 100 * (close - our_llv) / (hhv_calculated - our_llv)
    print(f"  验证：100 * ({close} - {our_llv}) / ({hhv_calculated:.4f} - {our_llv}) = {verify_changqi2:.2f}")
    
    # 分析可能的原因
    print(f"\n可能的原因分析：")
    print(f"1. 如果 LLV 应该是 {llv_calculated:.4f} 而不是 {our_llv}，差异为 {abs(llv_calculated - our_llv):.4f}")
    print(f"2. 如果 HHV 应该是 {hhv_calculated:.4f} 而不是 {our_hhv}，差异为 {abs(hhv_calculated - our_hhv):.4f}")
    
    # 检查是否有接近的价格值
    print(f"\n检查可能的价格值：")
    print(f"  反推的 LLV {llv_calculated:.4f} 约等于 {llv_calculated:.2f}")
    print(f"  反推的 HHV {hhv_calculated:.4f} 约等于 {hhv_calculated:.2f}")

if __name__ == "__main__":
    reverse_calculate()
