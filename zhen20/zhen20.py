from MyTT import *  # 导入MyTT库函数

def custom_indicator(CLOSE, LOW, HIGH, N1=3, N2=21, N3=1):
    """
    同花顺指标转换实现
    参数：
        CLOSE: 收盘价序列
        LOW: 最低价序列
        HIGH: 最高价序列
        N1: 短期周期（默认3）
        N2: 长期周期（默认21）
        N3: 缩放因子（默认1）
    """
    # 核心指标计算
    duanqi = 100 * (CLOSE - LLV(LOW, N1)) / (HHV(CLOSE, N1) - LLV(LOW, N1))   # 短期: 白线
    zhongqi = 100 * (CLOSE - LLV(LOW, 10)) / (HHV(CLOSE, 10) - LLV(LOW, 10))  # 中期: 黄线
    zhongchangqi = 100 * (CLOSE - LLV(LOW, 20)) / (HHV(CLOSE, 20) - LLV(LOW, 20))  # 中长期: 紫线
    changqi = 100 * (CLOSE - LLV(LOW, N2)) / (HHV(CLOSE, N2) - LLV(LOW, N2))  # 长期: 红线, 加粗
    
    # 信号条件计算
    buy_signal1 = IF((duanqi <= 6) & (zhongqi <= 6) & (zhongchangqi <= 6) & (changqi <= 6), -30, 0)  # 四线归零买
    buy_signal2 = IF((duanqi <= 20) & (changqi >= 60), -30, 0)  # 白线下20买
    buy_signal3 = IF((CROSS(duanqi, changqi)) & (changqi < 20), -30, 0)  # 白穿红线买
    buy_signal4 = IF((CROSS(duanqi, zhongqi)) & (zhongqi < 30), -30, 0)  # 白穿黄线买
    
    # 参考线
    A = 80 * N3  # 压力线
    B = 20 * N3  # 支撑线
    
    # 返回所有计算结果
    return {
        '短期': RD(duanqi), '中期': RD(zhongqi), 
        '中长期': RD(zhongchangqi), '长期': RD(changqi),
        '四线归零买': buy_signal1, '白线下20买': buy_signal2,
        '白穿红线买': buy_signal3, '白穿黄线买': buy_signal4,
        'A': A, 'B': B
    }