#!/usr/bin/env python3
"""
检查数据格式问题，找出2025-07-16日期21天内的真实最低价
"""

import pandas as pd
import numpy as np
import os

def load_stock_data(stock_code):
    """加载股票数据"""
    data_path = os.path.join('..', 'data', f'{stock_code}.csv')
    data = pd.read_csv(data_path)
    data['date'] = pd.to_datetime(data['date'])
    return data

def check_data_format():
    """检查数据格式"""
    data = load_stock_data("002203")
    
    # 找到2025-07-16的索引
    target_date = '2025-07-16'
    target_idx = data[data['date'] == target_date].index[0]
    
    print(f"目标日期：{target_date}")
    print(f"目标索引：{target_idx}")
    
    # 获取前21天的数据（包括目标日期）
    start_idx = target_idx - 20  # 前20天 + 目标日期 = 21天
    period_data = data.iloc[start_idx:target_idx + 1].copy()
    
    print(f"\n21天数据范围：{period_data['date'].min()} 到 {period_data['date'].max()}")
    print(f"数据条数：{len(period_data)}")
    
    print(f"\n21天内的价格数据：")
    for i, (_, row) in enumerate(period_data.iterrows()):
        # 检查数据格式
        is_high_precision = '.' in str(row['low']) and len(str(row['low']).split('.')[1]) > 4
        format_type = "复权格式" if is_high_precision else "正常格式"
        
        print(f"  {i+1:2d}. {row['date'].strftime('%Y-%m-%d')}: "
              f"low={row['low']:.2f} ({format_type})")
    
    # 找出最低价
    min_low = period_data['low'].min()
    min_date = period_data[period_data['low'] == min_low]['date'].iloc[0]
    
    print(f"\n21天内最低价：{min_low}")
    print(f"最低价日期：{min_date.strftime('%Y-%m-%d')}")
    
    # 检查是否是9.64
    if abs(min_low - 9.64) < 0.01:
        print("✅ 最低价确实是9.64左右")
    else:
        print(f"❌ 最低价不是9.64，而是{min_low}")
    
    # 重新计算长期指标
    close_val = data.loc[target_idx, 'close']
    high_21 = period_data['close'].max()
    low_21 = period_data['low'].min()
    
    print(f"\n重新计算长期指标：")
    print(f"收盘价 C = {close_val}")
    print(f"21天最高收盘价 HHV(C,21) = {high_21}")
    print(f"21天最低价 LLV(L,21) = {low_21}")
    
    changqi = 100 * (close_val - low_21) / (high_21 - low_21)
    print(f"长期 = 100 * ({close_val} - {low_21}) / ({high_21} - {low_21}) = {changqi:.2f}")
    
    # 与预期值比较
    expected = 85.11
    print(f"\n预期值：{expected}")
    print(f"计算值：{changqi:.2f}")
    print(f"差异：{abs(changqi - expected):.2f}")
    
    if abs(changqi - expected) < 0.1:
        print("✅ 计算结果与预期值匹配！")
    else:
        print("❌ 计算结果与预期值不匹配")

if __name__ == "__main__":
    check_data_format()
