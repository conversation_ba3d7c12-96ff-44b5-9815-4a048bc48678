#!/usr/bin/env python3
"""
验证 zhen20.py 实现的正确性
根据 plan.md 中的验证数据进行测试
"""

import pandas as pd
import numpy as np
import os
import sys
from zhen20 import custom_indicator

def load_stock_data(stock_code):
    """加载股票数据"""
    data_path = os.path.join('..', 'data', f'{stock_code}.csv')
    if not os.path.exists(data_path):
        raise FileNotFoundError(f"数据文件不存在: {data_path}")
    
    data = pd.read_csv(data_path)
    data['date'] = pd.to_datetime(data['date'])
    return data

def test_zhen20_indicator():
    """测试 zhen20 指标实现"""
    print("=" * 60)
    print("测试 zhen20 指标实现")
    print("=" * 60)
    
    # 加载 002203 股票数据
    stock_code = "002203"
    data = load_stock_data(stock_code)
    
    print(f"加载股票 {stock_code} 数据，共 {len(data)} 条记录")
    print(f"数据时间范围：{data['date'].min()} 到 {data['date'].max()}")
    
    # 计算指标
    result = custom_indicator(
        CLOSE=data['close'],
        LOW=data['low'], 
        HIGH=data['high'],
        N1=3,
        N2=21,
        N3=1
    )
    
    # 将结果添加到数据中
    for key, value in result.items():
        data[key] = value
    
    # 验证特定日期的数据
    test_dates = ['2025-07-15', '2025-07-16']
    expected_values = {
        '2025-07-15': {'短期': 55.56, '长期': 91.49, 'A': 80, 'B': 20},
        '2025-07-16': {'短期': 6.67, '长期': 85.11, 'A': 80, 'B': 20}
    }
    
    print("\n验证结果：")
    print("-" * 60)
    
    for test_date in test_dates:
        print(f"\n日期：{test_date}")
        
        # 查找对应日期的数据
        date_data = data[data['date'] == test_date]
        
        if date_data.empty:
            print(f"  ❌ 未找到日期 {test_date} 的数据")
            continue
        
        row = date_data.iloc[0]
        expected = expected_values[test_date]
        
        print(f"  预期值 vs 计算值：")
        
        # 检查各个指标
        for indicator in ['短期', '长期', 'A', 'B']:
            expected_val = expected[indicator]
            calculated_val = row[indicator]
            
            # 对于浮点数，允许小的误差
            if indicator in ['短期', '长期']:
                tolerance = 0.1  # 允许0.1的误差
                is_correct = abs(calculated_val - expected_val) <= tolerance
            else:
                is_correct = calculated_val == expected_val
            
            status = "✅" if is_correct else "❌"
            print(f"    {indicator}: {expected_val} vs {calculated_val:.2f} {status}")
    
    # 显示最近几天的完整数据
    print("\n最近5天的完整指标数据：")
    print("-" * 80)
    
    recent_data = data.tail(5)[['date', '短期', '中期', '中长期', '长期', 'A', 'B']]
    for _, row in recent_data.iterrows():
        print(f"{row['date'].strftime('%Y-%m-%d')}: "
              f"短期={row['短期']:.2f}, 中期={row['中期']:.2f}, "
              f"中长期={row['中长期']:.2f}, 长期={row['长期']:.2f}, "
              f"A={row['A']}, B={row['B']}")
    
    # 检查买入信号
    print("\n买入信号检查（最近5天）：")
    print("-" * 80)
    
    signal_columns = ['四线归零买', '白线下20买', '白穿红线买', '白穿黄线买']
    recent_signals = data.tail(5)[['date'] + signal_columns]
    
    for _, row in recent_signals.iterrows():
        signals = []
        for col in signal_columns:
            if row[col] != 0:
                signals.append(f"{col}={row[col]}")
        
        signal_str = ", ".join(signals) if signals else "无信号"
        print(f"{row['date'].strftime('%Y-%m-%d')}: {signal_str}")

if __name__ == "__main__":
    try:
        test_zhen20_indicator()
    except Exception as e:
        print(f"测试过程中出现错误：{e}")
        import traceback
        traceback.print_exc()
